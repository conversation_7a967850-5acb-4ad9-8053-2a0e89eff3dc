import React from 'react';
import MCQOptions from './QuestionTypes/MCQOptions';
import MultipleSelectOptions from './QuestionTypes/MultipleSelectOptions';
import ShortAnswerInput from './QuestionTypes/ShortAnswerInput';
import CodeEditor from './QuestionTypes/CodeEditor';

const OptionsList = ({
    options,
    selectedOption,
    onOptionSelect,
    questionType = 'mcq',
    disabled = false
}) => {
    // Render appropriate component based on question type
    const renderQuestionInput = () => {
        switch (questionType) {
            case 'mcq':
                return (
                    <MCQOptions
                        options={options}
                        selectedOption={selectedOption}
                        onOptionSelect={onOptionSelect}
                        disabled={disabled}
                    />
                );

            case 'multiple':
                return (
                    <MultipleSelectOptions
                        options={options}
                        selectedOptions={selectedOption}
                        onOptionSelect={onOptionSelect}
                        disabled={disabled}
                    />
                );

            case 'short-answer':
                return (
                    <ShortAnswerInput
                        value={selectedOption || ''}
                        onAnswerChange={onOptionSelect}
                        disabled={disabled}
                        placeholder="Type your answer here..."
                    />
                );

            case 'code':
                return (
                    <CodeEditor
                        value={selectedOption || ''}
                        onAnswerChange={onOptionSelect}
                        disabled={disabled}
                        placeholder="// Write your code here...\n\n"
                        language="javascript"
                    />
                );

            default:
                // Fallback for backward compatibility
                return (
                    <MCQOptions
                        options={options}
                        selectedOption={selectedOption}
                        onOptionSelect={onOptionSelect}
                        disabled={disabled}
                    />
                );
        }
    };

    return (
        <div className="space-y-2">
            {renderQuestionInput()}
        </div>
    );
};

export default OptionsList;