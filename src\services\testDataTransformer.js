// Test Data Transformer
// Converts API response data to the format expected by existing components

/**
 * Transform API test data to component-compatible format
 * @param {Object} apiData - Raw API response data
 * @returns {Object} - Transformed test data
 */
export const transformTestData = (apiData) => {
    if (!apiData) {
        throw new Error('No test data provided');
    }

    // Handle nested data structure - API returns { success, message, data: { actual test data } }
    const testData = apiData.data || apiData;

    if (!testData) {
        throw new Error('No test data found in API response');
    }

    const {
        _id,
        testName,
        description,
        duration,
        totalPoints,
        passingScore,
        scheduledDate,
        endDate,
        instructions,
        allowedAttempts,
        randomizeQuestions,
        showResults,
        company,
        questions,
        participantStatus,
        totalQuestions
    } = testData;

    // Transform questions to match existing component structure
    if (!questions || !Array.isArray(questions)) {
        throw new Error('Questions data is missing or invalid');
    }

    const transformedQuestions = questions.map((question, index) => {
        const {
            _id: questionId,
            questionId: originalQuestionId,
            questionText,
            questionType,
            category,
            difficulty,
            options,
            points,
            explanation
        } = question;

        // Transform options to match existing format
        const transformedOptions = options.map((option, optionIndex) => ({
            id: option._id,
            label: String.fromCharCode(65 + optionIndex), // A, B, C, D...
            text: option.text,
            isCorrect: option.isCorrect
        }));

        // Determine correct answer(s) based on question type
        let correctAnswer;
        if (questionType === 'MCQ') {
            const correctOption = transformedOptions.find(opt => opt.isCorrect);
            correctAnswer = correctOption ? correctOption.id : null;
        } else if (questionType === 'Multiple-Select') {
            correctAnswer = transformedOptions
                .filter(opt => opt.isCorrect)
                .map(opt => opt.id);
        } else if (questionType === 'Short-Answer' || questionType === 'Code') {
            // For text-based questions, correct answer would be stored differently
            // For now, we'll set it to null as it requires manual evaluation
            correctAnswer = null;
        }

        // Map question types to internal format
        let internalType;
        switch (questionType) {
            case 'MCQ':
                internalType = 'mcq';
                break;
            case 'Multiple-Select':
                internalType = 'multiple';
                break;
            case 'Short-Answer':
                internalType = 'short-answer';
                break;
            case 'Code':
                internalType = 'code';
                break;
            default:
                // Backward compatibility for old format
                internalType = questionType === 'Single-Select' ? 'mcq' : 'multiple';
        }

        return {
            id: questionId,
            originalQuestionId,
            title: questionText,
            description: '', // API doesn't provide separate description
            type: internalType,
            questionType: questionType, // Keep original for reference
            difficulty: difficulty.toLowerCase(),
            category,
            points,
            timeLimit: null, // API doesn't provide per-question time limit
            options: transformedOptions,
            correctAnswer,
            explanation
        };
    });

    // Transform main test data
    return {
        id: _id,
        title: testName,
        description: description || '',
        duration: duration * 60, // Convert minutes to seconds
        totalQuestions: totalQuestions || questions.length,
        passingScore,
        totalPoints,
        scheduledDate,
        endDate,
        instructions: instructions || '',
        allowedAttempts,
        randomizeQuestions,
        showResults,
        company,
        questions: transformedQuestions,
        participantStatus
    };
};

/**
 * Transform test instructions data for TestInstructions component
 * @param {Object} apiData - Raw API response data
 * @returns {Object} - Transformed instructions data
 */
export const transformInstructionsData = (apiData) => {
    if (!apiData) {
        throw new Error('No instructions data provided');
    }

    const {
        _id,
        testName,
        description,
        duration,
        totalQuestions,
        passingScore,
        scheduledDate,
        instructions,
        company
    } = apiData;

    return {
        testId: _id,
        title: testName,
        description: description || 'A comprehensive assessment to evaluate your skills and knowledge.',
        duration: duration * 60, // Convert minutes to seconds
        totalQuestions,
        passingScore,
        timePerQuestion: Math.floor((duration * 60) / totalQuestions), // Calculate average time per question
        testDate: scheduledDate ? new Date(scheduledDate).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
        testTime: scheduledDate ? new Date(scheduledDate).toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', hour12: true }) : '10:00 AM',
        candidate: {
            name: 'Test Candidate', // Will be populated from user context
            email: '<EMAIL>', // Will be populated from user context
            id: 'CAND_001' // Will be populated from user context
        },
        organization: company?.name || 'Test Organization',
        companyLogo: company?.logo || null,
        instructions: {
            general: [
                'This is a timed assessment that evaluates your knowledge and problem-solving abilities.',
                'Answer all questions to the best of your ability.',
                'Questions may include multiple choice and single select formats.',
                'Some questions may have partial scoring for multiple-choice answers.'
            ],
            specific: [
                'Carefully read each question before selecting your answer.',
                'For multiple-choice questions, select ALL correct answers.',
                'Use the question navigation panel to move between questions.',
                'You can change your answers until you submit the test.',
                instructions || 'Follow all test guidelines and instructions carefully.'
            ],
            restrictions: [
                'No external resources, books, or notes are allowed.',
                'Calculator use is permitted for mathematical calculations.',
                'Communication with others during the test is strictly prohibited.',
                'Screen recording or photography of test content is not allowed.'
            ]
        },
        systemRequirements: {
            browser: 'Chrome 90+, Firefox 88+, Safari 14+, Edge 90+',
            internet: 'Stable broadband connection (minimum 1 Mbps)',
            screen: 'Minimum 1024x768 resolution',
            os: 'Windows 10+, macOS 10.15+, or Linux Ubuntu 18.04+'
        }
    };
};

/**
 * Transform user answers to API submission format
 * @param {Object} answers - User answers from component state
 * @param {Array} questions - Questions array for reference
 * @returns {Object} - API-compatible answers format
 */
export const transformAnswersForSubmission = (answers, questions) => {
    const transformedAnswers = {};

    Object.keys(answers).forEach(questionId => {
        const question = questions.find(q => q.id === questionId);
        const userAnswer = answers[questionId];

        if (question && userAnswer !== null && userAnswer !== undefined) {
            // Map internal types back to API format
            let apiQuestionType;
            switch (question.type) {
                case 'mcq':
                    apiQuestionType = 'MCQ';
                    break;
                case 'multiple':
                    apiQuestionType = 'Multiple-Select';
                    break;
                case 'short-answer':
                    apiQuestionType = 'Short-Answer';
                    break;
                case 'code':
                    apiQuestionType = 'Code';
                    break;
                default:
                    // Backward compatibility
                    apiQuestionType = question.type === 'single' ? 'MCQ' : 'Multiple-Select';
            }

            // Handle different answer formats based on question type
            let answerData;
            if (question.type === 'mcq') {
                answerData = {
                    selectedOptions: [userAnswer],
                    textAnswer: null
                };
            } else if (question.type === 'multiple') {
                answerData = {
                    selectedOptions: Array.isArray(userAnswer) ? userAnswer : [userAnswer],
                    textAnswer: null
                };
            } else if (question.type === 'short-answer' || question.type === 'code') {
                answerData = {
                    selectedOptions: [],
                    textAnswer: userAnswer
                };
            } else {
                // Fallback for backward compatibility
                answerData = {
                    selectedOptions: Array.isArray(userAnswer) ? userAnswer : [userAnswer],
                    textAnswer: null
                };
            }

            transformedAnswers[questionId] = {
                questionId: question.originalQuestionId || questionId,
                questionType: apiQuestionType,
                ...answerData,
                isCorrect: null, // Will be calculated on server
                points: 0 // Will be calculated on server
            };
        }
    });

    return transformedAnswers;
};

/**
 * Calculate test progress and statistics
 * @param {Object} answers - User answers
 * @param {Array} questions - Questions array
 * @returns {Object} - Progress statistics
 */
export const calculateTestProgress = (answers, questions) => {
    const totalQuestions = questions.length;
    const answeredQuestions = Object.keys(answers).filter(questionId => {
        const answer = answers[questionId];
        if (Array.isArray(answer)) {
            return answer.length > 0;
        }
        return answer !== null && answer !== undefined && answer !== '';
    }).length;

    const progressPercentage = totalQuestions > 0 ? Math.round((answeredQuestions / totalQuestions) * 100) : 0;

    return {
        totalQuestions,
        answeredQuestions,
        unansweredQuestions: totalQuestions - answeredQuestions,
        progressPercentage
    };
};

/**
 * Validate test data structure
 * @param {Object} testData - Test data to validate
 * @returns {Object} - Validation result
 */
export const validateTestData = (testData) => {
    const errors = [];

    if (!testData) {
        errors.push('Test data is required');
        return { isValid: false, errors };
    }

    if (!testData.id) errors.push('Test ID is required');
    if (!testData.title) errors.push('Test title is required');
    if (!testData.duration || testData.duration <= 0) errors.push('Valid test duration is required');
    if (!testData.questions || !Array.isArray(testData.questions) || testData.questions.length === 0) {
        errors.push('Test questions are required');
    }

    // Validate questions
    if (testData.questions) {
        testData.questions.forEach((question, index) => {
            if (!question.id) errors.push(`Question ${index + 1}: ID is required`);
            if (!question.title) errors.push(`Question ${index + 1}: Title is required`);
            if (!question.options || !Array.isArray(question.options) || question.options.length === 0) {
                errors.push(`Question ${index + 1}: Options are required`);
            }
        });
    }

    return {
        isValid: errors.length === 0,
        errors
    };
};

export default {
    transformTestData,
    transformInstructionsData,
    transformAnswersForSubmission,
    calculateTestProgress,
    validateTestData
};
