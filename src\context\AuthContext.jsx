import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { authApi, apiUtils } from '../services/api';

// Auth Context
const AuthContext = createContext();

// Action types
const AUTH_ACTIONS = {
    SET_LOADING: 'SET_LOADING',
    SET_ERROR: 'SET_ERROR',
    SET_USER: 'SET_USER',
    SET_AUTHENTICATED: 'SET_AUTHENTICATED',
    LOGOUT: 'LOGOUT',
    CLEAR_ERROR: 'CLEAR_ERROR'
};

// Initial state
const initialState = {
    user: null,
    isAuthenticated: false,
    loading: true,
    error: null,
    token: null
};

// Reducer function
const authReducer = (state, action) => {
    switch (action.type) {
        case AUTH_ACTIONS.SET_LOADING:
            return {
                ...state,
                loading: action.payload
            };
            
        case AUTH_ACTIONS.SET_ERROR:
            return {
                ...state,
                error: action.payload,
                loading: false
            };
            
        case AUTH_ACTIONS.SET_USER:
            return {
                ...state,
                user: action.payload,
                isAuthenticated: !!action.payload,
                loading: false,
                error: null
            };
            
        case AUTH_ACTIONS.SET_AUTHENTICATED:
            return {
                ...state,
                isAuthenticated: action.payload,
                loading: false
            };
            
        case AUTH_ACTIONS.LOGOUT:
            return {
                ...initialState,
                loading: false
            };
            
        case AUTH_ACTIONS.CLEAR_ERROR:
            return {
                ...state,
                error: null
            };
            
        default:
            return state;
    }
};

// Auth Context Provider
export const AuthProvider = ({ children }) => {
    const [state, dispatch] = useReducer(authReducer, initialState);

    // Check authentication status on mount
    useEffect(() => {
        checkAuthStatus();
    }, []);

    // Check if user is authenticated
    const checkAuthStatus = async () => {
        dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: true });

        const token = apiUtils.getAuthToken();
        if (!token) {
            dispatch({ type: AUTH_ACTIONS.SET_AUTHENTICATED, payload: false });
            dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: false });
            return;
        }

        // For now, just check if token exists and decode it (since verify endpoint doesn't exist)
        try {
            // Decode the JWT token to get user info (basic validation)
            const tokenPayload = JSON.parse(atob(token.split('.')[1]));
            const currentTime = Date.now() / 1000;

            if (tokenPayload.exp && tokenPayload.exp > currentTime) {
                // Token is valid and not expired
                const user = {
                    id: tokenPayload.id,
                    email: tokenPayload.email,
                    name: tokenPayload.name || 'User',
                    role: tokenPayload.role
                };
                dispatch({ type: AUTH_ACTIONS.SET_USER, payload: user });
            } else {
                // Token is expired
                apiUtils.removeAuthToken();
                dispatch({ type: AUTH_ACTIONS.SET_AUTHENTICATED, payload: false });
            }
        } catch (error) {
            // Invalid token
            apiUtils.removeAuthToken();
            dispatch({ type: AUTH_ACTIONS.SET_AUTHENTICATED, payload: false });
        }

        dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: false });
    };

    // Login function
    const login = async (credentials) => {
        dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: true });
        dispatch({ type: AUTH_ACTIONS.CLEAR_ERROR });

        try {
            const response = await authApi.login(credentials);
            console.log('Login response:', response); // Debug log

            if (response.success) {
                const { user, token } = response.data; // User and token are in response.data
                console.log('Login successful, user:', user, 'token:', token); // Debug log

                // Store token
                apiUtils.setAuthToken(token);

                // Update state
                dispatch({ type: AUTH_ACTIONS.SET_USER, payload: user });
                dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: false });

                console.log('User state updated, should be authenticated now'); // Debug log
                return { success: true, user };
            } else {
                throw new Error(response.message || 'Login failed');
            }
        } catch (error) {
            console.error('Login error:', error); // Debug log
            const errorMessage = error.message || 'Login failed. Please try again.';
            dispatch({ type: AUTH_ACTIONS.SET_ERROR, payload: errorMessage });
            dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: false });
            return { success: false, error: errorMessage };
        }
    };

    // Logout function
    const logout = async () => {
        try {
            await authApi.logout();
        } catch (error) {
            console.error('Logout API call failed:', error);
        } finally {
            // Always clear local state and token
            apiUtils.removeAuthToken();
            dispatch({ type: AUTH_ACTIONS.LOGOUT });
        }
    };

    // Register function (if needed)
    const register = async (userData) => {
        dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: true });
        dispatch({ type: AUTH_ACTIONS.CLEAR_ERROR });

        try {
            // Implement registration logic here if needed
            // const response = await authApi.register(userData);
            
            // For now, just return success
            return { success: true };
        } catch (error) {
            const errorMessage = error.message || 'Registration failed. Please try again.';
            dispatch({ type: AUTH_ACTIONS.SET_ERROR, payload: errorMessage });
            return { success: false, error: errorMessage };
        }
    };

    // Clear error
    const clearError = () => {
        dispatch({ type: AUTH_ACTIONS.CLEAR_ERROR });
    };

    // Refresh token
    const refreshToken = async () => {
        try {
            const response = await authApi.refreshToken();
            if (response.success) {
                apiUtils.setAuthToken(response.data.token);
                return true;
            }
            return false;
        } catch (error) {
            console.error('Token refresh failed:', error);
            return false;
        }
    };

    // Context value
    const contextValue = {
        // State
        user: state.user,
        isAuthenticated: state.isAuthenticated,
        loading: state.loading,
        error: state.error,
        
        // Actions
        login,
        logout,
        register,
        clearError,
        checkAuthStatus,
        refreshToken,
        
        // Utility functions
        isLoggedIn: () => state.isAuthenticated && !!state.user,
        getUserRole: () => state.user?.role || null,
        getUserId: () => state.user?.id || null,
        getUserName: () => state.user?.name || '',
        getUserEmail: () => state.user?.email || ''
    };

    return (
        <AuthContext.Provider value={contextValue}>
            {children}
        </AuthContext.Provider>
    );
};

// Custom hook to use Auth Context
export const useAuth = () => {
    const context = useContext(AuthContext);
    if (!context) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
};

// Higher-order component for protected routes
export const withAuth = (Component) => {
    return function AuthenticatedComponent(props) {
        const { isAuthenticated, loading } = useAuth();
        
        if (loading) {
            return (
                <div className="min-h-screen flex items-center justify-center">
                    <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
                </div>
            );
        }
        
        if (!isAuthenticated) {
            // Redirect to login or show login component
            window.location.href = '/login';
            return null;
        }
        
        return <Component {...props} />;
    };
};

export default AuthContext;
