// API Service Layer for Test Application
const API_BASE_URL = 'http://localhost:5000/api';

// API Response wrapper
class ApiResponse {
    constructor(success, data, message, error = null) {
        this.success = success;
        this.data = data;
        this.message = message;
        this.error = error;
    }
}

// Custom API Error class
class ApiError extends Error {
    constructor(message, status, data = null) {
        super(message);
        this.name = 'ApiError';
        this.status = status;
        this.data = data;
    }
}

// Generic API request handler
const apiRequest = async (endpoint, options = {}) => {
    const url = `${API_BASE_URL}${endpoint}`;

    const defaultOptions = {
        headers: {
            'Content-Type': 'application/json',
        },
        credentials: 'include', // Include cookies in requests
    };

    // Add authentication token if available (for Bearer token support)
    const token = localStorage.getItem('authToken');
    if (token) {
        defaultOptions.headers.Authorization = `Bearer ${token}`;
    }

    const config = {
        ...defaultOptions,
        ...options,
        headers: {
            ...defaultOptions.headers,
            ...options.headers,
        },
    };

    try {
        const response = await fetch(url, config);
        const data = await response.json();

        if (!response.ok) {
            throw new ApiError(
                data.message || 'API request failed',
                response.status,
                data
            );
        }

        return new ApiResponse(true, data, data.message);
    } catch (error) {
        if (error instanceof ApiError) {
            throw error;
        }
        
        // Network or other errors
        throw new ApiError(
            error.message || 'Network error occurred',
            0,
            null
        );
    }
};

// Authentication API functions
export const authApi = {
    // Login user
    login: async (credentials) => {
        return apiRequest('/auth/login', {
            method: 'POST',
            body: JSON.stringify(credentials),
        });
    },

    // Logout user
    logout: async () => {
        return apiRequest('/auth/logout', {
            method: 'POST',
        });
    },

    // Verify token
    verifyToken: async () => {
        return apiRequest('/auth/verify');
    },

    // Refresh token
    refreshToken: async () => {
        return apiRequest('/auth/refresh', {
            method: 'POST',
        });
    },
};

// Test API functions
export const testApi = {
    // Get test questions by test ID
    getTestQuestions: async (testId) => {
        return apiRequest(`/student/tests/${testId}/questions`);
    },

    // Get test instructions
    getTestInstructions: async (testId) => {
        return apiRequest(`/student/tests/${testId}/instructions`);
    },

    // Submit test answers
    submitTestAnswers: async (testId, answers, metadata = {}) => {
        return apiRequest(`/student/tests/${testId}/submit`, {
            method: 'POST',
            body: JSON.stringify({
                answers,
                metadata: {
                    ...metadata,
                    submittedAt: new Date().toISOString(),
                },
            }),
        });
    },

    // Save answers (auto-save)
    saveAnswers: async (testId, answers) => {
        return apiRequest(`/student/tests/${testId}/save`, {
            method: 'POST',
            body: JSON.stringify({ answers }),
        });
    },

    // Get test result
    getTestResult: async (testId) => {
        return apiRequest(`/student/tests/${testId}/result`);
    },

    // Start test session
    startTestSession: async (testId) => {
        return apiRequest(`/student/tests/${testId}/start`, {
            method: 'POST',
        });
    },

    // End test session
    endTestSession: async (testId, reason = 'completed') => {
        return apiRequest(`/student/tests/${testId}/end`, {
            method: 'POST',
            body: JSON.stringify({ reason }),
        });
    },
};

// Student API functions
export const studentApi = {
    // Get student profile
    getProfile: async () => {
        return apiRequest('/student/profile');
    },

    // Update student profile
    updateProfile: async (profileData) => {
        return apiRequest('/student/profile', {
            method: 'PUT',
            body: JSON.stringify(profileData),
        });
    },

    // Get student test history
    getTestHistory: async () => {
        return apiRequest('/student/tests/history');
    },
};

// Utility functions
export const apiUtils = {
    // Check if user is authenticated
    isAuthenticated: () => {
        const token = localStorage.getItem('authToken');
        const cookieToken = document.cookie
            .split('; ')
            .find(row => row.startsWith('AuthToken='))
            ?.split('=')[1];
        return !!(token || cookieToken);
    },

    // Set authentication token (both localStorage and cookie)
    setAuthToken: (token) => {
        localStorage.setItem('authToken', token);
        // Set cookie with appropriate options (remove secure for localhost)
        document.cookie = `AuthToken=${token}; path=/; samesite=strict`;
    },

    // Remove authentication token (both localStorage and cookie)
    removeAuthToken: () => {
        localStorage.removeItem('authToken');
        // Remove cookie by setting expiry date in the past
        document.cookie = 'AuthToken=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';
    },

    // Get authentication token (prefer localStorage, fallback to cookie)
    getAuthToken: () => {
        const token = localStorage.getItem('authToken');
        if (token) return token;

        // Fallback to cookie
        const cookieToken = document.cookie
            .split('; ')
            .find(row => row.startsWith('AuthToken='))
            ?.split('=')[1];
        return cookieToken || null;
    },

    // Handle API errors
    handleApiError: (error) => {
        console.error('API Error:', error);
        
        if (error.status === 401) {
            // Unauthorized - redirect to login
            apiUtils.removeAuthToken();
            window.location.href = '/login';
        } else if (error.status === 403) {
            // Forbidden
            console.error('Access forbidden');
        } else if (error.status === 404) {
            // Not found
            console.error('Resource not found');
        } else if (error.status === 500) {
            // Server error
            console.error('Server error occurred');
        }
        
        return error;
    },
};

// Export the main API object
export default {
    auth: authApi,
    test: testApi,
    student: studentApi,
    utils: apiUtils,
};
