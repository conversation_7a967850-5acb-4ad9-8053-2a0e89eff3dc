import React, { createContext, useContext, useReducer, useEffect, useCallback } from 'react';
import { testApi, apiUtils } from '../services/api';
import { transformTestData, transformAnswersForSubmission, calculateTestProgress } from '../services/testDataTransformer';

// Test Context
const TestContext = createContext();

// Action types
const TEST_ACTIONS = {
    SET_LOADING: 'SET_LOADING',
    SET_ERROR: 'SET_ERROR',
    SET_TEST_DATA: 'SET_TEST_DATA',
    SET_CURRENT_QUESTION: 'SET_CURRENT_QUESTION',
    SET_ANSWER: 'SET_ANSWER',
    SET_ANSWERS: 'SET_ANSWERS',
    SET_TIME_LEFT: 'SET_TIME_LEFT',
    SET_TEST_ACTIVE: 'SET_TEST_ACTIVE',
    SET_TEST_COMPLETED: 'SET_TEST_COMPLETED',
    SET_QUESTION_TIME_SPENT: 'SET_QUESTION_TIME_SPENT',
    SET_TAB_SWITCH_COUNT: 'SET_TAB_SWITCH_COUNT',
    SET_SUBMISSION_STATUS: 'SET_SUBMISSION_STATUS',
    RESET_TEST: 'RESET_TEST',
    AUTO_SAVE_ANSWERS: 'AUTO_SAVE_ANSWERS'
};

// Initial state
const initialState = {
    // Test data
    testData: null,
    testId: null,
    
    // Loading and error states
    loading: false,
    error: null,
    
    // Question navigation
    currentQuestionIndex: 0,
    
    // Answers and progress
    answers: {},
    questionTimeSpent: {},
    
    // Test timing
    timeLeft: 0,
    testStartTime: null,
    
    // Test status
    isTestActive: false,
    isTestCompleted: false,
    isSubmitting: false,
    submissionStatus: null,
    
    // Anti-cheat measures
    tabSwitchCount: 0,
    
    // Auto-save
    lastAutoSave: null,
    autoSaveInterval: null
};

// Reducer function
const testReducer = (state, action) => {
    switch (action.type) {
        case TEST_ACTIONS.SET_LOADING:
            return {
                ...state,
                loading: action.payload,
                error: action.payload ? null : state.error
            };
            
        case TEST_ACTIONS.SET_ERROR:
            return {
                ...state,
                error: action.payload,
                loading: false
            };
            
        case TEST_ACTIONS.SET_TEST_DATA:
            return {
                ...state,
                testData: action.payload,
                testId: action.payload?.id || null,
                timeLeft: action.payload?.duration || 0,
                loading: false,
                error: null
            };
            
        case TEST_ACTIONS.SET_CURRENT_QUESTION:
            return {
                ...state,
                currentQuestionIndex: action.payload
            };
            
        case TEST_ACTIONS.SET_ANSWER:
            return {
                ...state,
                answers: {
                    ...state.answers,
                    [action.payload.questionId]: action.payload.answer
                }
            };
            
        case TEST_ACTIONS.SET_ANSWERS:
            return {
                ...state,
                answers: action.payload
            };
            
        case TEST_ACTIONS.SET_TIME_LEFT:
            return {
                ...state,
                timeLeft: action.payload
            };
            
        case TEST_ACTIONS.SET_TEST_ACTIVE:
            return {
                ...state,
                isTestActive: action.payload,
                testStartTime: action.payload ? new Date() : state.testStartTime
            };
            
        case TEST_ACTIONS.SET_TEST_COMPLETED:
            return {
                ...state,
                isTestCompleted: action.payload,
                isTestActive: !action.payload
            };
            
        case TEST_ACTIONS.SET_QUESTION_TIME_SPENT:
            return {
                ...state,
                questionTimeSpent: {
                    ...state.questionTimeSpent,
                    [action.payload.questionId]: action.payload.timeSpent
                }
            };
            
        case TEST_ACTIONS.SET_TAB_SWITCH_COUNT:
            return {
                ...state,
                tabSwitchCount: action.payload
            };
            
        case TEST_ACTIONS.SET_SUBMISSION_STATUS:
            return {
                ...state,
                isSubmitting: action.payload.isSubmitting,
                submissionStatus: action.payload.status
            };
            
        case TEST_ACTIONS.AUTO_SAVE_ANSWERS:
            return {
                ...state,
                lastAutoSave: new Date()
            };
            
        case TEST_ACTIONS.RESET_TEST:
            return {
                ...initialState
            };
            
        default:
            return state;
    }
};

// Test Context Provider
export const TestProvider = ({ children }) => {
    const [state, dispatch] = useReducer(testReducer, initialState);

    // Load test data
    const loadTestData = useCallback(async (testId) => {
        dispatch({ type: TEST_ACTIONS.SET_LOADING, payload: true });

        try {
            const response = await testApi.getTestQuestions(testId);
            const transformedData = transformTestData(response.data);

            dispatch({ type: TEST_ACTIONS.SET_TEST_DATA, payload: transformedData });

            return transformedData;
        } catch (error) {
            const handledError = apiUtils.handleApiError(error);
            dispatch({ type: TEST_ACTIONS.SET_ERROR, payload: handledError.message });
            throw error;
        } finally {
            dispatch({ type: TEST_ACTIONS.SET_LOADING, payload: false });
        }
    }, []);

    // Set current question
    const setCurrentQuestion = useCallback((index) => {
        if (index >= 0 && index < (state.testData?.questions?.length || 0)) {
            dispatch({ type: TEST_ACTIONS.SET_CURRENT_QUESTION, payload: index });
        }
    }, [state.testData?.questions?.length]);

    // Set answer for a question
    const setAnswer = useCallback((questionId, answer) => {
        dispatch({
            type: TEST_ACTIONS.SET_ANSWER,
            payload: { questionId, answer }
        });
    }, []);

    // Navigate to next question
    const nextQuestion = () => {
        const nextIndex = state.currentQuestionIndex + 1;
        if (nextIndex < (state.testData?.questions?.length || 0)) {
            setCurrentQuestion(nextIndex);
        }
    };

    // Navigate to previous question
    const previousQuestion = () => {
        const prevIndex = state.currentQuestionIndex - 1;
        if (prevIndex >= 0) {
            setCurrentQuestion(prevIndex);
        }
    };

    // Start test
    const startTest = async () => {
        try {
            if (state.testId) {
                await testApi.startTestSession(state.testId);
            }
            dispatch({ type: TEST_ACTIONS.SET_TEST_ACTIVE, payload: true });
        } catch (error) {
            console.error('Failed to start test session:', error);
            // Continue with local test start even if API call fails
            dispatch({ type: TEST_ACTIONS.SET_TEST_ACTIVE, payload: true });
        }
    };

    // Submit test
    const submitTest = async (metadata = {}) => {
        dispatch({ 
            type: TEST_ACTIONS.SET_SUBMISSION_STATUS, 
            payload: { isSubmitting: true, status: 'submitting' } 
        });

        try {
            const transformedAnswers = transformAnswersForSubmission(
                state.answers, 
                state.testData?.questions || []
            );

            const submissionMetadata = {
                ...metadata,
                timeSpent: state.testData?.duration - state.timeLeft,
                questionTimeSpent: state.questionTimeSpent,
                tabSwitchCount: state.tabSwitchCount,
                testStartTime: state.testStartTime,
                submittedAt: new Date().toISOString()
            };

            await testApi.submitTestAnswers(state.testId, transformedAnswers, submissionMetadata);
            
            dispatch({ type: TEST_ACTIONS.SET_TEST_COMPLETED, payload: true });
            dispatch({ 
                type: TEST_ACTIONS.SET_SUBMISSION_STATUS, 
                payload: { isSubmitting: false, status: 'success' } 
            });

            return true;
        } catch (error) {
            const handledError = apiUtils.handleApiError(error);
            dispatch({ 
                type: TEST_ACTIONS.SET_SUBMISSION_STATUS, 
                payload: { isSubmitting: false, status: 'error' } 
            });
            throw handledError;
        }
    };

    // Auto-save answers
    const autoSaveAnswers = async () => {
        if (!state.testId || !state.isTestActive) return;

        try {
            await testApi.saveAnswers(state.testId, state.answers);
            dispatch({ type: TEST_ACTIONS.AUTO_SAVE_ANSWERS });
        } catch (error) {
            console.error('Auto-save failed:', error);
        }
    };

    // Update time left
    const updateTimeLeft = (timeLeft) => {
        dispatch({ type: TEST_ACTIONS.SET_TIME_LEFT, payload: timeLeft });
    };

    // Update question time spent
    const updateQuestionTimeSpent = (questionId, timeSpent) => {
        dispatch({ 
            type: TEST_ACTIONS.SET_QUESTION_TIME_SPENT, 
            payload: { questionId, timeSpent } 
        });
    };

    // Update tab switch count
    const updateTabSwitchCount = (count) => {
        dispatch({ type: TEST_ACTIONS.SET_TAB_SWITCH_COUNT, payload: count });
    };

    // Reset test
    const resetTest = () => {
        dispatch({ type: TEST_ACTIONS.RESET_TEST });
    };

    // Get current question
    const getCurrentQuestion = () => {
        if (!state.testData?.questions || state.currentQuestionIndex < 0) return null;
        return state.testData.questions[state.currentQuestionIndex];
    };

    // Check if question is answered
    const isQuestionAnswered = (questionId) => {
        const answer = state.answers[questionId];
        if (Array.isArray(answer)) {
            return answer.length > 0;
        }
        return answer !== null && answer !== undefined && answer !== '';
    };

    // Get test progress
    const getTestProgress = () => {
        if (!state.testData?.questions) return { totalQuestions: 0, answeredQuestions: 0, progressPercentage: 0 };
        return calculateTestProgress(state.answers, state.testData.questions);
    };

    // Auto-save effect
    useEffect(() => {
        if (state.isTestActive && Object.keys(state.answers).length > 0) {
            const interval = setInterval(autoSaveAnswers, 30000); // Auto-save every 30 seconds
            return () => clearInterval(interval);
        }
    }, [state.isTestActive, state.answers]);

    // Context value
    const contextValue = {
        // State
        ...state,
        
        // Actions
        loadTestData,
        setCurrentQuestion,
        setAnswer,
        nextQuestion,
        previousQuestion,
        startTest,
        submitTest,
        autoSaveAnswers,
        updateTimeLeft,
        updateQuestionTimeSpent,
        updateTabSwitchCount,
        resetTest,
        
        // Computed values
        getCurrentQuestion,
        isQuestionAnswered,
        getTestProgress,
        
        // Current question (computed)
        currentQuestion: getCurrentQuestion(),
        
        // Progress (computed)
        progress: getTestProgress()
    };

    return (
        <TestContext.Provider value={contextValue}>
            {children}
        </TestContext.Provider>
    );
};

// Custom hook to use Test Context
export const useTest = () => {
    const context = useContext(TestContext);
    if (!context) {
        throw new Error('useTest must be used within a TestProvider');
    }
    return context;
};

export default TestContext;
