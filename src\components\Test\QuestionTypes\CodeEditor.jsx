import React, { useState, useEffect } from 'react';
import { Code, Copy, RotateCcw } from 'lucide-react';

const CodeEditor = ({
    value = '',
    onAnswerChange,
    disabled = false,
    placeholder = "// Write your code here...\n\n",
    language = 'javascript'
}) => {
    const [localValue, setLocalValue] = useState(value);
    const [copied, setCopied] = useState(false);

    useEffect(() => {
        setLocalValue(value);
    }, [value]);

    const handleChange = (e) => {
        const newValue = e.target.value;
        setLocalValue(newValue);
        onAnswerChange(newValue);
    };

    const handleKeyDown = (e) => {
        // Handle tab key for indentation
        if (e.key === 'Tab') {
            e.preventDefault();
            const start = e.target.selectionStart;
            const end = e.target.selectionEnd;
            const newValue = localValue.substring(0, start) + '    ' + localValue.substring(end);
            setLocalValue(newValue);
            onAnswerChange(newValue);
            
            // Set cursor position after the inserted tab
            setTimeout(() => {
                e.target.selectionStart = e.target.selectionEnd = start + 4;
            }, 0);
        }
        
        // Allow common keyboard shortcuts
        if (e.ctrlKey || e.metaKey) {
            const allowedKeys = ['a', 'c', 'v', 'x', 'z', 'y'];
            if (allowedKeys.includes(e.key.toLowerCase())) {
                return;
            }
        }
    };

    const handleCopy = async () => {
        try {
            await navigator.clipboard.writeText(localValue);
            setCopied(true);
            setTimeout(() => setCopied(false), 2000);
        } catch (err) {
            console.error('Failed to copy code:', err);
        }
    };

    const handleClear = () => {
        if (disabled) return;
        setLocalValue('');
        onAnswerChange('');
    };

    const getLanguageLabel = () => {
        const languages = {
            javascript: 'JavaScript',
            python: 'Python',
            java: 'Java',
            cpp: 'C++',
            c: 'C',
            csharp: 'C#',
            php: 'PHP',
            ruby: 'Ruby',
            go: 'Go',
            rust: 'Rust',
            sql: 'SQL'
        };
        return languages[language] || 'Code';
    };

    return (
        <div className="space-y-3">
            <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2 text-sm text-slate-600">
                    <Code className="w-4 h-4" />
                    <span>Code Question - {getLanguageLabel()}</span>
                </div>
                
                <div className="flex items-center space-x-2">
                    <button
                        onClick={handleCopy}
                        disabled={disabled || !localValue}
                        className="flex items-center space-x-1 px-2 py-1 text-xs text-slate-600 hover:text-slate-800 disabled:opacity-50 disabled:cursor-not-allowed"
                        title="Copy code"
                    >
                        <Copy className="w-3 h-3" />
                        <span>{copied ? 'Copied!' : 'Copy'}</span>
                    </button>
                    
                    <button
                        onClick={handleClear}
                        disabled={disabled || !localValue}
                        className="flex items-center space-x-1 px-2 py-1 text-xs text-slate-600 hover:text-slate-800 disabled:opacity-50 disabled:cursor-not-allowed"
                        title="Clear code"
                    >
                        <RotateCcw className="w-3 h-3" />
                        <span>Clear</span>
                    </button>
                </div>
            </div>
            
            <div className="relative">
                <textarea
                    value={localValue}
                    onChange={handleChange}
                    onKeyDown={handleKeyDown}
                    disabled={disabled}
                    placeholder={placeholder}
                    rows={12}
                    className={`
                        w-full px-4 py-3 border-2 rounded-lg resize-none transition-all duration-200
                        ${disabled 
                            ? 'bg-slate-50 border-slate-200 cursor-not-allowed opacity-50' 
                            : 'bg-slate-900 border-slate-300 focus:border-indigo-500 focus:ring-2 focus:ring-indigo-200'
                        }
                        text-slate-100 placeholder-slate-400 text-sm leading-relaxed
                        font-mono whitespace-pre
                    `}
                    style={{ 
                        minHeight: '300px',
                        backgroundColor: disabled ? '#f8fafc' : '#0f172a',
                        color: disabled ? '#64748b' : '#f1f5f9'
                    }}
                    spellCheck={false}
                />
                
                {/* Line numbers indicator */}
                <div className="absolute bottom-2 left-2 text-xs text-slate-400">
                    Lines: {localValue.split('\n').length}
                </div>
                
                {/* Character count */}
                <div className="absolute bottom-2 right-2 text-xs text-slate-400">
                    {localValue.length} characters
                </div>
            </div>
            
            {/* Instructions */}
            <div className="text-xs text-slate-500 bg-slate-50 p-2 rounded border">
                <strong>Instructions:</strong> Write your code solution below. 
                Use Tab for indentation. Common keyboard shortcuts (Ctrl+C, Ctrl+V, etc.) are supported.
            </div>
        </div>
    );
};

export default CodeEditor;
