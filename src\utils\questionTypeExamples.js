// Example data showing all four question types
// This demonstrates how the API should structure different question types

export const questionTypeExamples = {
    // MCQ (Multiple Choice Question) - Single correct answer
    mcq: {
        "_id": "6880639df81d617bb71a4f57",
        "questionId": "687f79fa6fad6a0ea1666a6e",
        "questionText": "Which HTTP status code indicates a successful request?",
        "questionType": "MCQ",
        "category": "Backend",
        "difficulty": "Easy",
        "options": [
            {
                "text": "200 OK",
                "isCorrect": true,
                "_id": "687f79fa6fad6a0ea1666a6f"
            },
            {
                "text": "404 Not Found",
                "isCorrect": false,
                "_id": "687f79fa6fad6a0ea1666a70"
            },
            {
                "text": "500 Internal Server Error",
                "isCorrect": false,
                "_id": "687f79fa6fad6a0ea1666a71"
            },
            {
                "text": "301 Moved Permanently",
                "isCorrect": false,
                "_id": "687f79fa6fad6a0ea1666a72"
            }
        ],
        "points": 1,
        "explanation": "200 OK indicates a successful HTTP request."
    },

    // Multiple-Select - Multiple correct answers
    multipleSelect: {
        "_id": "6880639df81d617bb71a4f58",
        "questionId": "687f79fa6fad6a0ea1666a7e",
        "questionText": "Which of the following are 2xx HTTP status codes?",
        "questionType": "Multiple-Select",
        "category": "Backend",
        "difficulty": "Medium",
        "options": [
            {
                "text": "200 OK",
                "isCorrect": true,
                "_id": "687f79fa6fad6a0ea1666a7f"
            },
            {
                "text": "201 Created",
                "isCorrect": true,
                "_id": "687f79fa6fad6a0ea1666a80"
            },
            {
                "text": "204 No Content",
                "isCorrect": true,
                "_id": "687f79fa6fad6a0ea1666a81"
            },
            {
                "text": "404 Not Found",
                "isCorrect": false,
                "_id": "687f79fa6fad6a0ea1666a82"
            }
        ],
        "points": 2,
        "explanation": "404 is a 4xx (client error); others are 2xx."
    },

    // Short-Answer - Text input
    shortAnswer: {
        "_id": "6880639df81d617bb71a4f59",
        "questionId": "687f79fa6fad6a0ea1666a8e",
        "questionText": "Explain the difference between GET and POST HTTP methods.",
        "questionType": "Short-Answer",
        "category": "Backend",
        "difficulty": "Medium",
        "options": [], // No options for text-based questions
        "points": 3,
        "explanation": "GET is used to retrieve data and is idempotent, while POST is used to submit data and can have side effects."
    },

    // Code - Code editor
    code: {
        "_id": "6880639df81d617bb71a4f60",
        "questionId": "687f79fa6fad6a0ea1666a9e",
        "questionText": "Write a JavaScript function that takes an array of numbers and returns the sum of all even numbers.",
        "questionType": "Code",
        "category": "Programming",
        "difficulty": "Hard",
        "options": [], // No options for code questions
        "points": 5,
        "explanation": "The function should filter even numbers and sum them using reduce or a loop."
    }
};

// Example of how answers would be stored for each question type
export const answerExamples = {
    // MCQ answer - single option ID
    mcq: "687f79fa6fad6a0ea1666a6f",
    
    // Multiple-Select answer - array of option IDs
    multipleSelect: [
        "687f79fa6fad6a0ea1666a7f",
        "687f79fa6fad6a0ea1666a80",
        "687f79fa6fad6a0ea1666a81"
    ],
    
    // Short-Answer answer - text string
    shortAnswer: "GET is used to retrieve data from the server and is idempotent, meaning multiple identical requests have the same effect. POST is used to submit data to the server and can have side effects, such as creating new resources or modifying existing ones.",
    
    // Code answer - code string
    code: `function sumEvenNumbers(numbers) {
    return numbers
        .filter(num => num % 2 === 0)
        .reduce((sum, num) => sum + num, 0);
}

// Alternative solution using a for loop
function sumEvenNumbers(numbers) {
    let sum = 0;
    for (let num of numbers) {
        if (num % 2 === 0) {
            sum += num;
        }
    }
    return sum;
}`
};

// Example of transformed data structure after processing
export const transformedExamples = {
    mcq: {
        id: "6880639df81d617bb71a4f57",
        originalQuestionId: "687f79fa6fad6a0ea1666a6e",
        title: "Which HTTP status code indicates a successful request?",
        type: "mcq",
        questionType: "MCQ",
        difficulty: "easy",
        category: "Backend",
        points: 1,
        options: [
            { id: "687f79fa6fad6a0ea1666a6f", label: "A", text: "200 OK", isCorrect: true },
            { id: "687f79fa6fad6a0ea1666a70", label: "B", text: "404 Not Found", isCorrect: false },
            { id: "687f79fa6fad6a0ea1666a71", label: "C", text: "500 Internal Server Error", isCorrect: false },
            { id: "687f79fa6fad6a0ea1666a72", label: "D", text: "301 Moved Permanently", isCorrect: false }
        ],
        correctAnswer: "687f79fa6fad6a0ea1666a6f",
        explanation: "200 OK indicates a successful HTTP request."
    },
    
    shortAnswer: {
        id: "6880639df81d617bb71a4f59",
        originalQuestionId: "687f79fa6fad6a0ea1666a8e",
        title: "Explain the difference between GET and POST HTTP methods.",
        type: "short-answer",
        questionType: "Short-Answer",
        difficulty: "medium",
        category: "Backend",
        points: 3,
        options: [],
        correctAnswer: null,
        explanation: "GET is used to retrieve data and is idempotent, while POST is used to submit data and can have side effects."
    }
};
