
import { BrowserRouter, Route, Routes, Navigate } from 'react-router-dom';
import { AuthProvider } from './context/AuthContext';
import { TestProvider } from './context/TestContext';
import TestPage from './pages/TestPage';
import TestInstructions from './pages/TestInstructions';
import Login from './pages/Login';
import ResultPage from './pages/ResultPage';
import NotFound from './pages/NotFound';
import ProtectedRoute from './components/Layout/ProtectedRoute';
import './App.css';

function App() {
  return (
    <AuthProvider>
      <BrowserRouter>
        <Routes>
          {/* Default route - redirect to login */}
          <Route path="/" element={<Navigate to="/login" replace />} />

          {/* Public routes */}
          <Route path="/login" element={<Login />} />

          {/* Test application routes with testId parameter */}
          <Route path="/testapplication/:testId" element={
            <ProtectedRoute>
              <TestProvider>
                <Navigate to="instructions" replace />
              </TestProvider>
            </ProtectedRoute>
          } />

          <Route path="/testapplication/:testId/instructions" element={
            <ProtectedRoute>
              <TestProvider>
                <TestInstructions />
              </TestProvider>
            </ProtectedRoute>
          } />

          <Route path="/testapplication/:testId/test" element={
            <ProtectedRoute>
              <TestProvider>
                <TestPage />
              </TestProvider>
            </ProtectedRoute>
          } />

          <Route path="/testapplication/:testId/result" element={
            <ProtectedRoute>
              <TestProvider>
                <ResultPage />
              </TestProvider>
            </ProtectedRoute>
          } />

          {/* Legacy routes for backward compatibility */}
          <Route path="/" element={<Navigate to="/login" replace />} />
          <Route path="/instruction" element={<Navigate to="/login" replace />} />

          {/* 404 route */}
          <Route path="*" element={<NotFound />} />
        </Routes>
      </BrowserRouter>
    </AuthProvider>
  );
}

export default App;
