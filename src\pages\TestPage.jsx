import React, { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight, Send, AlertTriangle, Eye, EyeOff, Shield, Wifi, WifiOff } from 'lucide-react';
import { useParams, useNavigate } from 'react-router-dom';
import { useTest } from '../context/TestContext';
import OptionsList from '../components/Test/OptionsList';
import Timer from '../components/Test/Timer';
import QuestionCard from '../components/Test/QuestionCard';
import QuestionNavigation from '../components/Test/QuestionNavigation';
import Button from '../components/UI/Button';
import Modal, { ConfirmModal } from '../components/UI/Modal';

// Note: Dummy test data removed - now using real API data from TestContext

const TestPage = () => {
    // Get testId from URL params
    const { testId } = useParams();
    const navigate = useNavigate();

    // Test context
    const {
        testData,
        currentQuestionIndex,
        answers,
        timeLeft,
        isTestActive,
        isTestCompleted,
        loading,
        error,
        loadTestData,
        setCurrentQuestion,
        setAnswer,
        nextQuestion,
        previousQuestion,
        submitTest,
        startTest,
        updateTimeLeft,
        updateTabSwitchCount
    } = useTest();

    // Local state for UI and anti-cheat features
    const [questionTimeSpent, setQuestionTimeSpent] = useState({});

    // Additional UI state that's not in TestContext
    const [isTestActiveLocal, setIsTestActiveLocal] = useState(true);
    const [isTestCompletedLocal, setIsTestCompletedLocal] = useState(false);

    // Anti-cheat state
    const [tabSwitchCount, setTabSwitchCount] = useState(0);
    const [isFullscreen, setIsFullscreen] = useState(false);
    const [isOnline, setIsOnline] = useState(navigator.onLine);
    const [devToolsWarning, setDevToolsWarning] = useState(false);

    // UI state
    const [showSubmitModal, setShowSubmitModal] = useState(false);
    const [showTimeUpModal, setShowTimeUpModal] = useState(false);
    const [showWarningModal, setShowWarningModal] = useState(false);
    const [warningMessage, setWarningMessage] = useState('');
    const [isSubmitting, setIsSubmitting] = useState(false);

    // Load test data on component mount
    useEffect(() => {
        if (testId && !testData) {
            loadTestData(testId);
        }
    }, [testId, testData, loadTestData]);

    // Start test when test data is loaded and test is not active
    useEffect(() => {
        if (testData && !isTestActive && !isTestCompleted) {
            console.log('Starting test automatically...');
            startTest();
        }
    }, [testData, isTestActive, isTestCompleted, startTest]);

    // Current question and selected answer
    const currentQuestion = testData?.questions?.[currentQuestionIndex];
    const selectedAnswer = React.useMemo(() => {
        if (!currentQuestion) return null;
        const answer = answers[currentQuestion.id];

        switch (currentQuestion.type) {
            case 'multiple':
                return answer || [];
            case 'short-answer':
            case 'code':
                return answer || '';
            case 'mcq':
            default:
                return answer || null;
        }
    }, [currentQuestion, answers]);

    // Anti-cheat measures
    useEffect(() => {
        // Request fullscreen
        const requestFullscreen = () => {
            if (document.documentElement.requestFullscreen) {
                document.documentElement.requestFullscreen();
            }
        };

        // Tab switch detection
        const handleVisibilityChange = () => {
            if (document.hidden && (isTestActive && !isTestCompletedLocal)) {
                setTabSwitchCount(prev => prev + 1);
                setWarningMessage(`Warning: Tab switching detected! Count: ${tabSwitchCount + 1}/3`);
                setShowWarningModal(true);

                if (tabSwitchCount >= 2) {
                    handleAutoSubmit('Multiple tab switches detected');
                }
            }
        };

        // Fullscreen change detection
        const handleFullscreenChange = () => {
            setIsFullscreen(!!document.fullscreenElement);
            if (!document.fullscreenElement && (isTestActive && !isTestCompletedLocal)) {
                setWarningMessage('Please return to fullscreen mode to continue the test.');
                setShowWarningModal(true);
            }
        };

        // Network status
        const handleOnline = () => setIsOnline(true);
        const handleOffline = () => setIsOnline(false);

        // Dev tools detection (basic)
        const detectDevTools = () => {
            const threshold = 160;
            if (window.outerHeight - window.innerHeight > threshold ||
                window.outerWidth - window.innerWidth > threshold) {
                if (!devToolsWarning) {
                    setDevToolsWarning(true);
                    setWarningMessage('Developer tools detected! Please close dev tools to continue.');
                    setShowWarningModal(true);
                }
            }
        };

        // Prevent right-click and key combinations
        const preventCheating = (e) => {
            // Disable F12, Ctrl+Shift+I, Ctrl+Shift+J, Ctrl+U
            if (e.key === 'F12' ||
                (e.ctrlKey && e.shiftKey && (e.key === 'I' || e.key === 'J')) ||
                (e.ctrlKey && e.key === 'u')) {
                e.preventDefault();
                setWarningMessage('This action is not allowed during the test.');
                setShowWarningModal(true);
            }
        };

        const preventRightClick = (e) => {
            e.preventDefault();
            setWarningMessage('Right-click is disabled during the test.');
            setShowWarningModal(true);
        };

        if (isTestActive && !isTestCompletedLocal) {
            requestFullscreen();
            document.addEventListener('visibilitychange', handleVisibilityChange);
            document.addEventListener('fullscreenchange', handleFullscreenChange);
            document.addEventListener('keydown', preventCheating);
            document.addEventListener('contextmenu', preventRightClick);
            window.addEventListener('online', handleOnline);
            window.addEventListener('offline', handleOffline);

            const devToolsInterval = setInterval(detectDevTools, 1000);

            return () => {
                document.removeEventListener('visibilitychange', handleVisibilityChange);
                document.removeEventListener('fullscreenchange', handleFullscreenChange);
                document.removeEventListener('keydown', preventCheating);
                document.removeEventListener('contextmenu', preventRightClick);
                window.removeEventListener('online', handleOnline);
                window.removeEventListener('offline', handleOffline);
                clearInterval(devToolsInterval);
            };
        }
    }, [isTestActive, isTestCompletedLocal, tabSwitchCount, devToolsWarning]);

    // Question timer
    useEffect(() => {
        const questionId = currentQuestion?.id;
        if (!questionId) return;

        const startTime = Date.now();

        return () => {
            const timeSpent = Math.floor((Date.now() - startTime) / 1000);
            setQuestionTimeSpent(prev => ({
                ...prev,
                [questionId]: (prev[questionId] || 0) + timeSpent
            }));
        };
    }, [currentQuestionIndex, currentQuestion?.id]);

    // Auto-save answers
    useEffect(() => {
        const saveInterval = setInterval(() => {
            if (isTestActive && !isTestCompletedLocal) {
                // Auto-save to server would go here
                console.log('Auto-saving answers:', answers);
            }
        }, 30000); // Save every 30 seconds

        return () => clearInterval(saveInterval);
    }, [answers, isTestActive, isTestCompletedLocal]);

    const handleAnswerSelect = (answer) => {
        if (!currentQuestion || !currentQuestion.id) return;

        setAnswer(currentQuestion.id, answer);
    };

    const handleNext = () => {
        if (currentQuestionIndex < (testData?.questions?.length || 0) - 1) {
            nextQuestion();
        }
    };

    const handlePrevious = () => {
        if (currentQuestionIndex > 0) {
            previousQuestion();
        }
    };

    const handleTimeUp = () => {
        setIsTestActiveLocal(false);
        setShowTimeUpModal(true);
        // Auto-submit the test when time is up
        handleAutoSubmit('Time expired');
    };

    const handleAutoSubmit = async (reason) => {
        setIsTestActiveLocal(false);
        setIsTestCompletedLocal(true);
        console.log(`Test auto-submitted: ${reason}`);

        try {
            await submitTest({ reason, timestamp: new Date().toISOString() });
            navigate(`/testapplication/${testId}/result`);
        } catch (error) {
            console.error('Auto-submit failed:', error);
        }
    };

    const handleSubmitTest = async () => {
        setIsSubmitting(true);

        try {
            // Calculate results
            let score = 0;
            let totalPoints = 0;

            (testData?.questions || []).forEach(question => {
                totalPoints += question.points;
                const userAnswer = answers[question.id];

                switch (question.type) {
                    case 'mcq':
                        if (userAnswer === question.correctAnswer) {
                            score += question.points;
                        }
                        break;
                    case 'multiple':
                        if (userAnswer && Array.isArray(userAnswer) && Array.isArray(question.correctAnswer)) {
                            const correct = question.correctAnswer.every(ans => userAnswer.includes(ans)) &&
                                userAnswer.every(ans => question.correctAnswer.includes(ans));
                            if (correct) score += question.points;
                        }
                        break;
                    case 'short-answer':
                    case 'code':
                        // Text-based questions require manual evaluation
                        // For now, we'll give partial credit if answered
                        if (userAnswer && typeof userAnswer === 'string' && userAnswer.trim().length > 0) {
                            // This would typically be evaluated on the server
                            // For demo purposes, we'll give partial credit
                            score += question.points * 0.5; // 50% partial credit for attempting
                        }
                        break;
                    default:
                        // Backward compatibility
                        if (question.type === 'single' && userAnswer === question.correctAnswer) {
                            score += question.points;
                        }
                }
            });

            const percentage = Math.round((score / totalPoints) * 100);

            // Simulate API call
            await new Promise(resolve => setTimeout(resolve, 2000));

            const testResult = {
                testId: testData?.id,
                score,
                totalPoints,
                percentage,
                passed: percentage >= (testData?.passingScore || 70),
                answers,
                timeSpent: (testData?.duration || 0) - timeLeft,
                questionTimeSpent,
                tabSwitchCount,
                submittedAt: new Date().toISOString()
            };

            console.log('Test submitted:', testResult);
            setIsTestCompletedLocal(true);
            setIsTestActiveLocal(false);

            // Navigate to results page
            navigate(`/testapplication/${testId}/result`);

        } catch (error) {
            console.error('Submission failed:', error);
            setWarningMessage('Failed to submit test. Please try again.');
            setShowWarningModal(true);
        } finally {
            setIsSubmitting(false);
            setShowSubmitModal(false);
        }
    };

    const getAnsweredCount = () => {
        return Object.keys(answers).length;
    };

    const isAnswered = (questionId) => {
        const answer = answers[questionId];
        const question = testData?.questions?.find(q => q.id === questionId);

        if (!question) return false;

        switch (question.type) {
            case 'multiple':
                return Array.isArray(answer) && answer.length > 0;
            case 'short-answer':
            case 'code':
                return typeof answer === 'string' && answer.trim().length > 0;
            case 'mcq':
            default:
                return answer !== null && answer !== undefined && answer !== '';
        }
    };

    // Loading state
    if (loading) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-50 flex items-center justify-center">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                    <p className="text-gray-600">Loading test...</p>
                </div>
            </div>
        );
    }

    // Error state
    if (error) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-red-50 to-pink-50 flex items-center justify-center p-4">
                <div className="bg-white rounded-xl shadow-2xl p-8 max-w-md w-full text-center">
                    <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <AlertTriangle className="w-8 h-8 text-red-600" />
                    </div>
                    <h1 className="text-2xl font-bold text-gray-900 mb-2">Error Loading Test</h1>
                    <p className="text-gray-600 mb-4">{error}</p>
                    <Button
                        onClick={() => window.location.reload()}
                        className="bg-red-600 hover:bg-red-700"
                    >
                        Retry
                    </Button>
                </div>
            </div>
        );
    }

    // No test data
    if (!testData) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 flex items-center justify-center p-4">
                <div className="bg-white rounded-xl shadow-2xl p-8 max-w-md w-full text-center">
                    <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <AlertTriangle className="w-8 h-8 text-gray-600" />
                    </div>
                    <h1 className="text-2xl font-bold text-gray-900 mb-2">Test Not Found</h1>
                    <p className="text-gray-600">The requested test could not be found.</p>
                </div>
            </div>
        );
    }

    if (isTestCompleted || isTestCompletedLocal) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex items-center justify-center p-4">
                <div className="bg-white rounded-xl shadow-2xl p-8 max-w-md w-full text-center">
                    <div className="mb-6">
                        <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <Shield className="w-8 h-8 text-green-600" />
                        </div>
                        <h1 className="text-2xl font-bold text-gray-900 mb-2">Test Completed!</h1>
                        <p className="text-gray-600">Your responses have been submitted successfully.</p>
                    </div>

                    <div className="bg-gray-50 rounded-lg p-4 mb-6">
                        <div className="grid grid-cols-2 gap-4 text-sm">
                            <div>
                                <span className="text-gray-500">Questions:</span>
                                <div className="font-semibold">{getAnsweredCount()}/{testData?.totalQuestions || 0}</div>
                            </div>
                            <div>
                                <span className="text-gray-500">Time Used:</span>
                                <div className="font-semibold">
                                    {Math.floor(((testData?.duration || 0) - timeLeft) / 60)}m {((testData?.duration || 0) - timeLeft) % 60}s
                                </div>
                            </div>
                        </div>
                    </div>

                    <p className="text-sm text-gray-500">
                        Results will be available shortly. You may now close this window.
                    </p>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
            {/* Header */}
            <div className="bg-white shadow-sm border-b border-slate-200 sticky top-0 z-40">
                <div className="max-w-7xl mx-auto px-4 py-3">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                            <h1 className="text-xl font-bold text-gray-900">{testData?.title || 'Test'}</h1>
                            <div className="flex items-center space-x-2">
                                {!isOnline && <WifiOff className="w-5 h-5 text-red-500" />}
                                {isOnline && <Wifi className="w-5 h-5 text-green-500" />}
                                {!isFullscreen && <Eye className="w-5 h-5 text-orange-500" />}
                                {isFullscreen && <EyeOff className="w-5 h-5 text-green-500" />}
                            </div>
                        </div>

                        <div className="flex items-center space-x-4">
                            <div className="text-sm text-gray-600">
                                {getAnsweredCount()}/{testData?.totalQuestions || 0} answered
                            </div>
                            <Timer
                                initialTime={timeLeft}
                                onTimeUp={handleTimeUp}
                                isActive={isTestActive}
                            />
                        </div>
                    </div>
                </div>
            </div>

            {/* Question Navigation Sidebar */}
            <QuestionNavigation
                questions={testData?.questions || []}
                currentQuestionIndex={currentQuestionIndex}
                answers={answers}
                onQuestionSelect={setCurrentQuestion}
                isAnswered={isAnswered}
            />

            {/* Mobile Question Navigation */}
            <div className="lg:hidden bg-white shadow-sm border-b border-slate-200 sticky top-14 z-20">
                <div className="max-w-4xl mx-auto p-3">
                    <div className="flex items-center justify-between mb-2">
                        <h3 className="font-semibold text-slate-900 text-sm">Questions</h3>
                        <span className="text-xs text-slate-600">
                            {Object.keys(answers).length}/{testData?.questions?.length || 0}
                        </span>
                    </div>
                    <div className="max-h-32 overflow-y-auto scrollbar-thin">
                        <div className="grid grid-cols-10 gap-1.5 pb-2">
                            {(testData?.questions || []).map((question, index) => (
                                <button
                                    key={question.id}
                                    onClick={() => setCurrentQuestion(index)}
                                    className={`
                                        w-7 h-7 rounded-md text-xs font-medium transition-all duration-200
                                        ${index === currentQuestionIndex
                                            ? 'bg-indigo-600 text-white shadow-md'
                                            : isAnswered(question.id)
                                                ? 'bg-emerald-100 text-emerald-800 border border-emerald-300'
                                                : 'bg-slate-100 text-slate-600 border border-slate-200'
                                        }
                                    `}
                                >
                                    {index + 1}
                                </button>
                            ))}
                        </div>
                    </div>
                </div>
            </div>

            {/* Main Content */}
            <div className="max-w-4xl mx-auto p-4 lg:ml-80">
                <div className="space-y-4">
                    <QuestionCard
                        question={currentQuestion}
                        currentIndex={currentQuestionIndex}
                        totalQuestions={testData.totalQuestions}
                        timeSpent={questionTimeSpent[currentQuestion?.id] || 0}
                        isAnswered={isAnswered(currentQuestion?.id)}
                        difficulty={currentQuestion?.difficulty}
                    />

                    {/* Options */}
                    <div className="bg-white rounded-lg shadow-md border border-slate-200 p-4">
                        <OptionsList
                            options={currentQuestion?.options || []}
                            selectedOption={selectedAnswer}
                            onOptionSelect={handleAnswerSelect}
                            questionType={currentQuestion?.type}
                            disabled={!isTestActive || isTestCompletedLocal}
                        />
                    </div>

                    {/* Navigation */}
                    <div className="flex items-center justify-between bg-white rounded-lg shadow-md border border-slate-200 p-4">
                        <Button
                            variant="secondary"
                            onClick={handlePrevious}
                            disabled={currentQuestionIndex === 0}
                            icon={ChevronLeft}
                        >
                            Previous
                        </Button>

                        <div className="flex items-center space-x-4">
                            {currentQuestionIndex === (testData?.questions?.length || 0) - 1 ? (
                                <Button
                                    variant="success"
                                    onClick={() => setShowSubmitModal(true)}
                                    icon={Send}
                                    size="lg"
                                >
                                    Submit Test
                                </Button>
                            ) : (
                                <Button
                                    variant="primary"
                                    onClick={handleNext}
                                    icon={ChevronRight}
                                    iconPosition="right"
                                >
                                    Next
                                </Button>
                            )}
                        </div>
                    </div>
                </div>
            </div>

            {/* Modals */}
            <ConfirmModal
                isOpen={showSubmitModal}
                onClose={() => setShowSubmitModal(false)}
                onConfirm={handleSubmitTest}
                title="Submit Test"
                message={`Are you sure you want to submit your test? You have answered ${getAnsweredCount()} out of ${testData.totalQuestions} questions.`}
                confirmText={isSubmitting ? "Submitting..." : "Submit"}
                type="info"
            />

            <Modal
                isOpen={showTimeUpModal}
                onClose={() => { }}
                title="Time's Up!"
                type="warning"
                showCloseButton={false}
                closeOnOverlayClick={false}
                closeOnEscape={false}
                footer={
                    <Button
                        variant="primary"
                        onClick={handleSubmitTest}
                        loading={isSubmitting}
                    >
                        Submit Test
                    </Button>
                }
            >
                <p className="text-gray-600 mb-4">
                    The test time has expired. Your test will be submitted automatically.
                </p>
                <div className="bg-orange-50 p-4 rounded-lg">
                    <p className="text-sm text-orange-800">
                        Answered: {getAnsweredCount()}/{testData.totalQuestions} questions
                    </p>
                </div>
            </Modal>

            <Modal
                isOpen={showWarningModal}
                onClose={() => setShowWarningModal(false)}
                title="Warning"
                type="warning"
                footer={
                    <Button variant="primary" onClick={() => setShowWarningModal(false)}>
                        I Understand
                    </Button>
                }
            >
                <p className="text-gray-600">{warningMessage}</p>
                {tabSwitchCount > 0 && (
                    <div className="mt-4 bg-red-50 p-4 rounded-lg">
                        <p className="text-sm text-red-800">
                            Tab switches: {tabSwitchCount}/3 (Test will auto-submit after 3 violations)
                        </p>
                    </div>
                )}
            </Modal>
        </div>
    );
};

export default TestPage;