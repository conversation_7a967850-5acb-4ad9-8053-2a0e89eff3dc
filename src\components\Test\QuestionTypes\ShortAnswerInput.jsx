import React, { useState, useEffect } from 'react';
import { FileText } from 'lucide-react';

const ShortAnswerInput = ({
    value = '',
    onAnswerChange,
    disabled = false,
    placeholder = "Type your answer here..."
}) => {
    const [localValue, setLocalValue] = useState(value);

    useEffect(() => {
        setLocalValue(value);
    }, [value]);

    const handleChange = (e) => {
        const newValue = e.target.value;
        setLocalValue(newValue);
        onAnswerChange(newValue);
    };

    const handleKeyDown = (e) => {
        // Allow common keyboard shortcuts
        if (e.ctrlKey || e.metaKey) {
            // Allow Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X, Ctrl+Z
            const allowedKeys = ['a', 'c', 'v', 'x', 'z'];
            if (allowedKeys.includes(e.key.toLowerCase())) {
                return;
            }
        }
    };

    return (
        <div className="space-y-3">
            <div className="flex items-center space-x-2 text-sm text-slate-600">
                <FileText className="w-4 h-4" />
                <span>Short Answer Question</span>
            </div>
            
            <div className="relative">
                <textarea
                    value={localValue}
                    onChange={handleChange}
                    onKeyDown={handleKeyDown}
                    disabled={disabled}
                    placeholder={placeholder}
                    rows={4}
                    className={`
                        w-full px-4 py-3 border-2 rounded-lg resize-none transition-all duration-200
                        ${disabled 
                            ? 'bg-slate-50 border-slate-200 cursor-not-allowed opacity-50' 
                            : 'bg-white border-slate-200 focus:border-indigo-500 focus:ring-2 focus:ring-indigo-200'
                        }
                        text-slate-900 placeholder-slate-400 text-sm leading-relaxed
                    `}
                    style={{ minHeight: '100px' }}
                />
                
                {/* Character count */}
                <div className="absolute bottom-2 right-2 text-xs text-slate-400">
                    {localValue.length} characters
                </div>
            </div>
            
            {/* Instructions */}
            <div className="text-xs text-slate-500 bg-slate-50 p-2 rounded border">
                <strong>Instructions:</strong> Provide a clear and concise answer. 
                You can use multiple lines if needed.
            </div>
        </div>
    );
};

export default ShortAnswerInput;
